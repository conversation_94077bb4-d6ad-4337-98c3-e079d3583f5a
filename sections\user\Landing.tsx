// src/app/page.tsx (or relevant file)
import React, {
  Suspense,
  useCallback,
  useEffect,
  useState,
  useMemo,
  useRef,
} from "react";
import dynamic from "next/dynamic";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
// Import modular components
import CustomMapDrawer from "@/components/map/CustomMapDrawer";
import GoogleMapComponent from "@/components/map/GoogleMapComponent";
import SplitViewLayout from "@/components/layouts/SplitViewLayout";
import { Button } from "@/components/ui/button";
import { MapPin, AlertCircle } from "lucide-react";
import clsx from "clsx"; // Import clsx for conditional classes

import {
  getAllCuisines,
  getAllHosts,
  getAllPublishedDishes,
  getDishesByMapBounds,
} from "@/api/host";
import {
  createUserProfile,
  getUserProfile,
  updateUserAddress,
  updateUserProfile,
} from "@/api/userProfile";
import Footer from "@/components/footer/footer";
import { HeaderBase } from "@/components/layouts/core/header-base";
import PhoneNumberModal from "@/components/popups/phone-number-popup";
import ScrollableTabs from "@/components/tabs/ScrollableTabs";
import { useAuthContext } from "@/context/hooks/use-auth-hook";
import { useBoolean } from "@/hooks/use-boolean";
// Import types from their correct locations
import { Cuisine, Dish, Host } from "@/types/host";
import { Address } from "@/types/address";
// interface Cuisine { /* ... */ }
// interface Dish { /* ... */ }
// interface Host { /* ... */ }

import "@/styles/map-markers.css";

// Dynamic Imports
import { useJsApiLoader } from "@react-google-maps/api";
import HomeDishCard from "@/components/cards/HomeDishCard";
import HomeSearchToolbar from "@/components/toolbars/SearchToolBar";
import DishCardSkeleton from "@/components/ui/dish-card-skeleton";
const AddressUserModal = dynamic(
  () => import("@/components/popups/address-user-popup"),
  { ssr: false }
);
// --- End Dynamic Imports ---

// --- Type Definitions ---
// Define adapter function types
interface DiningLocation {
  _id: string;
  locationName: string;
  images: string[];
}
// --- End Type Definitions ---

function Home() {
  const hamburgerMenu = useBoolean(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();

  // --- State Variables ---
  const [address, setAddress] = useState<string>(
    searchParams.get("address") || ""
  );
  // FIX: Initialize state correctly using searchParams
  const [dishName, setDishName] = useState<string>(
    searchParams.get("dishName") || ""
  );
  const [title, setWhen] = useState<string>(searchParams.get("title") || ""); // 'title' seems to map to 'when'
  const [offering, setOffering] = useState<string>(
    searchParams.get("offering") || ""
  );
  const [cuisinequery, setCuisineQuery] = useState<string>(
    searchParams.get("cuisine") || ""
  );
  // --- End State Initialization Fix ---
  const [allCuisines, setAllCuisines] = useState<
    { label: string; value: string }[]
  >([]);
  const [results, setResults] = useState<Host[]>([]); // Keep for backward compatibility
  const [dishes, setDishes] = useState<Dish[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { user } = useAuthContext();
  const [showPhoneModal, setShowPhoneModal] = useState<boolean>(false);
  const [showAddressModal, setShowAddressModal] = useState<boolean>(false);
  // Removed unused dishes state
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isSplitView, setIsSplitView] = useState(true);

  // Detect if we're on a large screen for split view
  useEffect(() => {
    const handleResize = () => {
      // setIsSplitView(window.innerWidth >= 1024); // lg breakpoint
    };

    // Set initial value
    handleResize();

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Clean up
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Determine if we should show the split view based on address search only
  const showSplitView = useMemo(() => {
    // Only show split view if we're on a large screen AND there's an active address search

    return isSplitView;
  }, [isSplitView]);
  const [selectedDish, setSelectedDish] = useState<Dish | null>(null);
  const [hoveredDishId, setHoveredDishId] = useState<string | null>(null);
  const [mapCenter, setMapCenter] = useState({
    lat: 40.712776,
    lng: -74.005974,
  });
  const [mapZoom, setMapZoom] = useState(1);
  const [mapBounds, setMapBounds] = useState<{
    north: number;
    south: number;
    east: number;
    west: number;
  } | null>(null);
  const [mapDishes, setMapDishes] = useState<Dish[]>([]);
  const [isMapLoading, setIsMapLoading] = useState(false);
  // Removed searchBounds state as it's no longer needed
  const [searchError, setSearchError] = useState<string | null>(null);
  const [lastSearchQuery, setLastSearchQuery] = useState<string>("");
  const [hasSearched, setHasSearched] = useState(false);
  // New state for storing pending autocomplete address data
  const [pendingAddressData, setPendingAddressData] = useState<{
    address: string;
    lat?: number;
    lng?: number;
    bounds?: google.maps.LatLngBounds;
  } | null>(null);
  // State to track executed search parameters (for URL and display)
  // Initialize from URL parameters if they exist (for page refresh/direct links)
  const [executedSearchParams, setExecutedSearchParams] = useState<{
    address: string;
    dishName: string;
    title: string;
    offering: string;
    cuisinequery: string;
  }>({
    address: searchParams.get("address") || "",
    dishName: searchParams.get("dishName") || "",
    title: searchParams.get("title") || "",
    offering: searchParams.get("offering") || "",
    cuisinequery: searchParams.get("cuisine") || "",
  });
  // --- End State Variables ---

  const googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || "";
  const libraries = useMemo(
    () => ["places"] as ("places" | "drawing" | "geometry" | "visualization")[],
    []
  );

  const { isLoaded, loadError } = useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: googleMapsApiKey,
    libraries: libraries,
  });

  // Map container style is now defined in GoogleMapComponent

  // --- Data Fetching ---
  const fetchCuisines = useCallback(async () => {
    try {
      const response = await getAllCuisines();
      const formattedCuisines =
        response?.data?.map((cuisine: Cuisine) => ({
          label: cuisine.name,
          value: cuisine.name, // Using name as value for filtering
        })) || [];
      setAllCuisines([
        { label: "All Cuisines", value: "" },
        ...formattedCuisines,
      ]);
    } catch (error) {
      console.error("Error fetching cuisines:", error);
      toast.error("Failed to load cuisines.");
    }
  }, []);

  const fetchDishes = useCallback(async () => {
    setIsLoading(true);
    const params = new URLSearchParams();
    if (address) params.set("address", address);
    if (dishName) params.set("dishName", dishName);
    if (title) params.set("title", title);
    if (offering) params.set("offering", offering);
    if (cuisinequery) params.set("cuisine", cuisinequery);
    const queryString = params.toString();
    console.log("Fetching dishes with query:", queryString);
    console.log("Search parameters:", {
      address,
      dishName,
      title,
      offering,
      cuisinequery,
    });

    try {
      // Use the new API to fetch dishes directly
      const response = await getAllPublishedDishes(queryString);
      const dishesData = response?.data || [];
      console.log("Fetched dishes:", dishesData);
      setDishes(dishesData);

      // Create a synthetic host structure for backward compatibility
      // Group dishes by host
      const hostMap = new Map();

      dishesData.forEach((dish: any) => {
        if (dish.host) {
          const hostId = dish.host._id;
          if (!hostMap.has(hostId)) {
            hostMap.set(hostId, {
              _id: hostId,
              title: dish.host.title || "Unknown Host",
              address: dish.host.address || null,
              dishes: [],
            });
          }
          hostMap.get(hostId).dishes.push(dish);
        }
      });

      const hostsData = Array.from(hostMap.values());
      setResults(hostsData);

      // Update map center if we have hosts with addresses
      if (
        hostsData.length > 0 &&
        hostsData[0].address?.latitude &&
        hostsData[0].address?.longitude &&
        !params.has("address") // Don't override if address was searched specifically
      ) {
        setMapCenter({
          lat: hostsData[0].address.latitude,
          lng: hostsData[0].address.longitude,
        });
        setMapZoom(13);
      }
    } catch (error) {
      console.error("Error fetching dishes:", error);
      toast.error("Failed to fetch dishes.");
      setDishes([]);
      setResults([]);
    } finally {
      setIsLoading(false);
    }
  }, [address, dishName, title, offering, cuisinequery]); // Dependencies are correct

  // Fetch dishes by map bounds with debouncing
  const fetchDishesByBounds = useCallback(
    async (bounds: {
      north: number;
      south: number;
      east: number;
      west: number;
    }) => {
      setIsMapLoading(true);
      try {
        const filters = {
          dishName: dishName || undefined,
          title: title || undefined,
          offering: offering || undefined,
          cuisine: cuisinequery || undefined,
        };

        const response = await getDishesByMapBounds(bounds, filters);
        const dishesData = response?.data || [];
        console.log("Fetched dishes by bounds:", dishesData);

        // Update map dishes
        setMapDishes(dishesData);

        // Create host structure for map markers
        const hostMap = new Map();
        dishesData.forEach((dish: any) => {
          if (dish.host) {
            const hostId = dish.host._id;
            if (!hostMap.has(hostId)) {
              hostMap.set(hostId, {
                _id: hostId,
                title: dish.host.title || "Unknown Host",
                address: dish.host.address || null,
                dishes: [],
              });
            }
            hostMap.get(hostId).dishes.push(dish);
          }
        });

        // If we're in split view, update the main results too
        if (showSplitView) {
          const hostsData = Array.from(hostMap.values());
          setResults(hostsData);
          setDishes(dishesData);
        }
      } catch (error) {
        console.error("Error fetching dishes by bounds:", error);
        // Don't show error toast for map bounds fetching to avoid spam
      } finally {
        setIsMapLoading(false);
      }
    },
    [dishName, title, offering, cuisinequery, showSplitView]
  );

  // Debounced version of fetchDishesByBounds
  const debouncedFetchDishesByBounds = useMemo(() => {
    let timeoutId: NodeJS.Timeout;
    return (bounds: {
      north: number;
      south: number;
      east: number;
      west: number;
    }) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        fetchDishesByBounds(bounds);
      }, 500); // 500ms debounce
    };
  }, [fetchDishesByBounds]);

  // Handle map bounds change
  const handleMapBoundsChange = useCallback(
    (bounds: { north: number; south: number; east: number; west: number }) => {
      setMapBounds(bounds);
      debouncedFetchDishesByBounds(bounds);
    },
    [debouncedFetchDishesByBounds]
  );

  // Adapter function to convert Dish to DishCardProps format
  const adaptDishForCard = useCallback((dish: Dish) => {
    // Create a mock diningLocation object if dish.diningLocation is a string
    const diningLocation: DiningLocation =
      typeof dish.diningLocation === "string"
        ? {
            _id: "location-" + dish._id, // Generate a temporary ID
            locationName: dish.diningLocation, // Use the string as the location name
            images: [], // Empty images array
          }
        : (dish.diningLocation as unknown as DiningLocation);

    // Ensure averageRating is always a number (default to 0 if undefined)
    const averageRating = dish.averageRating ?? 0;

    // Format availability to match DishCardProps
    const availability =
      dish.availability?.map((slot, index) => ({
        startTimeGMT: null,
        endTimeGMT: null,
        date:
          typeof slot.date === "string"
            ? slot.date
            : slot.date.toISOString().split("T")[0],
        startTime: slot.startTime,
        endTime: slot.endTime,
        _id: `slot-${index}-${dish._id}`, // Generate a temporary ID
      })) || [];

    return {
      ...dish,
      diningLocation,
      averageRating,
      availability,
    };
  }, []);
  // --- End Data Fetching ---

  // --- Effects ---
  useEffect(() => {
    fetchCuisines();
    // fetchDishes(); // Decide if this is needed
  }, [fetchCuisines /*, fetchDishes*/]);

  // Removed automatic fetchDishes call to eliminate redundant API calls
  // This will be replaced with bounds-based search logic after getAddressBounds is defined

  const updateURLParams = useCallback(
    (searchParams: {
      address: string;
      dishName: string;
      title: string;
      offering: string;
      cuisinequery: string;
    }) => {
      const params = new URLSearchParams();

      // Use helper function for cleaner param setting/deleting
      const setOrDeleteParam = (key: string, value: string) => {
        if (value) params.set(key, value);
        else params.delete(key);
      };

      setOrDeleteParam("address", searchParams.address);
      setOrDeleteParam("dishName", searchParams.dishName);
      setOrDeleteParam("title", searchParams.title);
      setOrDeleteParam("offering", searchParams.offering);
      setOrDeleteParam("cuisine", searchParams.cuisinequery);

      // Use push instead of replace if you want browser history for searches
      router.replace(`${pathname}?${params.toString()}`, { scroll: false });
    },
    [pathname, router]
  );

  // Removed automatic URL parameter updates
  // URL parameters now only update after search execution

  const handleProfileSetup = useCallback(async () => {
    if (!user?._id) return;
    try {
      const userProfile = await getUserProfile();
      if (!userProfile?.phoneNumber) {
        setShowPhoneModal(true);
      }
      // Optional: Check for address here too if it's mandatory after phone
      // else if (!userProfile?.address?.street) { // Check a required address field
      //   setShowAddressModal(true);
      // }
    } catch (error: any) {
      if (error.response?.status === 404) {
        try {
          const resp = await createUserProfile();
          if (resp?.user) {
            setShowPhoneModal(true); // Prompt for phone first after creation
          }
        } catch (createError) {
          console.error("Failed to create user profile:", createError);
          toast.error("Failed to setup user profile.");
        }
      } else {
        console.error("Failed to retrieve user profile:", error);
        // Avoid showing error toast for expected 404s or general retrieval issues unless critical
      }
    }
  }, [user?._id]);

  useEffect(() => {
    handleProfileSetup();
  }, [handleProfileSetup]);

  // Function to get bounds for an address using Google Geocoding API
  const getAddressBounds = useCallback(
    async (
      address: string
    ): Promise<{
      center: { lat: number; lng: number };
      bounds: { north: number; south: number; east: number; west: number };
      zoom: number;
    } | null> => {
      if (!isLoaded || !window.google) {
        console.warn("Google Maps API not loaded");
        return null;
      }

      try {
        const geocoder = new window.google.maps.Geocoder();
        const response = await geocoder.geocode({ address });

        if (response.results && response.results.length > 0) {
          const result = response.results[0];
          const location = result.geometry.location;
          const viewport = result.geometry.viewport;

          const center = {
            lat: location.lat(),
            lng: location.lng(),
          };

          const bounds = {
            north: viewport.getNorthEast().lat(),
            south: viewport.getSouthWest().lat(),
            east: viewport.getNorthEast().lng(),
            west: viewport.getSouthWest().lng(),
          };

          // Calculate appropriate zoom level based on bounds
          const latDiff = Math.abs(bounds.north - bounds.south);
          const lngDiff = Math.abs(bounds.east - bounds.west);
          const maxDiff = Math.max(latDiff, lngDiff);

          let zoom = 10; // Default zoom
          if (maxDiff < 0.01) zoom = 15; // Very small area (neighborhood)
          else if (maxDiff < 0.05) zoom = 13; // Small area (district)
          else if (maxDiff < 0.1) zoom = 12; // Medium area (city district)
          else if (maxDiff < 0.5) zoom = 10; // Large area (city)
          else zoom = 8; // Very large area (region)

          console.log("Geocoded address bounds:", {
            address,
            center,
            bounds,
            zoom,
          });
          return { center, bounds, zoom };
        }
      } catch (error) {
        console.error("Error geocoding address:", error);
      }

      return null;
    },
    [isLoaded]
  );

  // Convert address to bounds on page load and when address changes
  const convertAddressToBounds = useCallback(
    async (addressStr: string) => {
      if (!addressStr.trim() || !isLoaded) return null;

      try {
        console.log("Converting address to bounds:", addressStr);
        const geocodeResult = await getAddressBounds(addressStr);

        if (geocodeResult) {
          const bounds = geocodeResult.bounds;
          console.log("Address converted to bounds successfully:", bounds);
          return bounds;
        }
      } catch (error) {
        console.error("Error converting address to bounds:", error);
      }

      return null;
    },
    [isLoaded, getAddressBounds]
  );

  // Removed automatic search on parameter changes
  // Search now only triggers when search button is explicitly clicked
  // --- End Effects ---

  // --- Event Handlers ---
  const handleSearch = useCallback(async () => {
    // Clear previous errors and set loading state
    setSearchError(null);
    setIsLoading(true);
    setHasSearched(true);

    const searchQuery = `${address || ""} ${dishName || ""} ${title || ""} ${
      offering || ""
    } ${cuisinequery || ""}`.trim();
    setLastSearchQuery(searchQuery);

    try {
      // Validate search input
      if (!address && !dishName && !title && !offering && !cuisinequery) {
        const error = "Please enter at least one search criteria";
        setSearchError(error);
        toast.error(error);
        return;
      }

      // Handle address-based search using pending address data
      if (address && address.trim()) {
        let boundsToUse = null;

        // Check if we have pending address data with bounds (from autocomplete)
        if (pendingAddressData && pendingAddressData.bounds) {
          console.log(
            "Using pending address data with bounds:",
            pendingAddressData
          );

          const bounds = {
            north: pendingAddressData.bounds.getNorthEast().lat(),
            south: pendingAddressData.bounds.getSouthWest().lat(),
            east: pendingAddressData.bounds.getNorthEast().lng(),
            west: pendingAddressData.bounds.getSouthWest().lng(),
          };

          boundsToUse = bounds;

          // Update map center and zoom for the searched location
          const center = pendingAddressData.bounds.getCenter();
          setMapCenter({
            lat: center.lat(),
            lng: center.lng(),
          });

          // Calculate appropriate zoom level based on bounds
          const latDiff = Math.abs(bounds.north - bounds.south);
          const lngDiff = Math.abs(bounds.east - bounds.west);
          const maxDiff = Math.max(latDiff, lngDiff);

          let zoom = 10; // Default zoom
          if (maxDiff < 0.01) zoom = 15; // Very small area (neighborhood)
          else if (maxDiff < 0.05) zoom = 13; // Small area (district)
          else if (maxDiff < 0.1) zoom = 12; // Medium area (city district)
          else if (maxDiff < 0.5) zoom = 10; // Large area (city)
          else zoom = 8; // Very large area (region)

          setMapZoom(zoom);
          setMapBounds(bounds);
        }
        // Check if we have pending address data with coordinates but no bounds
        else if (
          pendingAddressData &&
          pendingAddressData.lat &&
          pendingAddressData.lng
        ) {
          console.log(
            "Using pending address data with coordinates:",
            pendingAddressData
          );

          // Update map center
          setMapCenter({
            lat: pendingAddressData.lat,
            lng: pendingAddressData.lng,
          });
          setMapZoom(14); // Default zoom for coordinate-based locations

          // Try to geocode to get bounds for search
          const bounds = await convertAddressToBounds(address);
          if (bounds) {
            boundsToUse = bounds;
          }
        }
        // No pending data or only address string - need to geocode
        else {
          console.log("No pending address data, geocoding address:", address);
          const bounds = await convertAddressToBounds(address);
          if (bounds) {
            boundsToUse = bounds;
          }
        }

        // Perform the search
        if (boundsToUse) {
          console.log("Search triggered with bounds:", boundsToUse);
          await fetchDishesByBounds(boundsToUse);
          toast.success(`Search completed for ${address}`);
        } else {
          // Fallback to regular search if bounds conversion fails
          console.log(
            "Bounds conversion failed, falling back to regular search"
          );
          await fetchDishes();
          toast.info("Location not found, showing general results");
        }

        // Clear pending address data after use
        setPendingAddressData(null);
      } else {
        // Use regular search if no address provided
        await fetchDishes();
        toast.success("Search completed");
      }

      // Update executed search parameters and URL after successful search
      const currentSearchParams = {
        address,
        dishName,
        title,
        offering,
        cuisinequery,
      };
      setExecutedSearchParams(currentSearchParams);
      updateURLParams(currentSearchParams);
    } catch (error) {
      console.error("Search error:", error);
      const errorMessage =
        "An error occurred while searching. Please try again.";
      setSearchError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [
    address,
    dishName,
    title,
    offering,
    cuisinequery,
    pendingAddressData,
    fetchDishesByBounds,
    fetchDishes,
    convertAddressToBounds,
    results.length,
  ]);

  // Define handleAddressChange - now only stores address data without triggering search
  const handleAddressChange = useCallback(
    (
      newAddress: string,
      lat?: number,
      lng?: number,
      bounds?: google.maps.LatLngBounds
    ) => {
      console.log("Address changed:", { newAddress, lat, lng, bounds });

      // Store the address data for later use when search button is clicked
      if (bounds) {
        // Store complete address data with bounds for geocoded addresses
        setPendingAddressData({
          address: newAddress,
          lat,
          lng,
          bounds,
        });
        console.log("Stored autocomplete address data with bounds:", {
          newAddress,
          lat,
          lng,
        });
      } else if (lat !== undefined && lng !== undefined) {
        // Store address data with coordinates but no bounds
        setPendingAddressData({
          address: newAddress,
          lat,
          lng,
        });
        console.log("Stored address data with coordinates:", {
          newAddress,
          lat,
          lng,
        });
      } else {
        // Store just the address string for manual input
        setPendingAddressData({
          address: newAddress,
        });
        console.log("Stored address string for later geocoding:", newAddress);
      }

      // Update the address state for display purposes
      setAddress(newAddress);
      console.log(
        "Address updated in state, search will be triggered manually"
      );
    },
    []
  );

  const dishSelectFilter = (value: string) => setDishName(value);
  const dateFilter = (value: string) => setWhen(value); // Maps to 'title' state
  const offeringFilter = (value: string) => setOffering(value);
  const cuisineFilter = (value: string) => {
    setCuisineQuery(value);
    setSelectedDish(null); // Close info window when filter changes
  };

  // Clear all filters function
  const handleClearFilters = useCallback(() => {
    // Reset all search input states
    setAddress("");
    setDishName("");
    setWhen("");
    setOffering("");
    setCuisineQuery("");
    setPendingAddressData(null);

    // Reset executed search parameters
    const emptyParams = {
      address: "",
      dishName: "",
      title: "",
      offering: "",
      cuisinequery: "",
    };
    setExecutedSearchParams(emptyParams);

    // Update URL to remove all parameters
    updateURLParams(emptyParams);

    // Reset search state
    setHasSearched(false);
    setSearchError(null);
    setLastSearchQuery("");

    toast.info("All filters cleared");
  }, [updateURLParams]);

  const handlePhoneSubmit = async (phoneNumberData: {
    phoneNumber: string;
  }) => {
    try {
      await updateUserProfile(phoneNumberData);
      setShowPhoneModal(false);
      // Check if address is needed next
      const userProfile = await getUserProfile(); // Re-fetch to check address
      if (!userProfile?.address?.street) {
        // Check a required field
        setShowAddressModal(true);
      }
      toast.success("Phone number updated!");
    } catch (error) {
      console.error("Failed to update phone number:", error);
      toast.error("Failed to update phone number.");
    }
  };

  const handleAddressSubmit = async (data: any) => {
    // Ensure data structure matches Address type or backend expectation
    const addressData: Partial<Address> & {
      latitude?: number;
      longitude?: number;
    } = {
      // Use Address type if imported
      street: data.street,
      city: data.city || "NA",
      state: data.state || "NA",
      postalCode: data.postalCode || "00000",
      country: data.country || "US",
      latitude: data.latitude, // Make sure these are passed from AddressUserModal
      longitude: data.longitude,
    };

    try {
      await updateUserAddress(addressData);
      setShowAddressModal(false);
      toast.success("Address updated successfully!");
    } catch (error) {
      console.error("Failed to update address:", error);
      toast.error("Failed to update address.");
    }
  };

  const handleDishMarkerClick = (dish: Dish, host: Host) => {
    setSelectedDish(dish);
    // Optional: Center map on the clicked dish marker
    // if (host.address?.latitude && host.address?.longitude) {
    //   setMapCenter({
    //     lat: host.address.latitude,
    //     lng: host.address.longitude,
    //   });
    // }
  };

  const handleInfoWindowClose = () => {
    setSelectedDish(null);
  };

  const handleDishClick = (dish: Dish, host: Host) => {
    setSelectedDish(dish);
    if (host.address?.latitude && host.address?.longitude) {
      setMapCenter({
        lat: host.address.latitude,
        lng: host.address.longitude,
      });
    }
  };

  // --- End Event Handlers ---

  // --- Render Logic ---
  const mapHosts = useMemo(() => {
    // Filter hosts with valid coordinates and dishes
    return results.filter(
      (host) =>
        host.address?.latitude != null && // Ensure non-null lat/lng
        host.address?.longitude != null &&
        host.dishes && // Ensure host has dishes array
        host.dishes.length > 0 // Ensure there's at least one dish
    );
  }, [results]);

  // Marker options are now defined in GoogleMapComponent

  // --- End Render Helper ---

  // Height of the header + search bar for calculations
  const headerHeight = 128; // Height of HeaderBase (64px) + Search Bar (64px)
  return (
    <React.Fragment>
      {/* Header - Always use HeaderBase */}
      <HeaderBase hamburgerMenu={hamburgerMenu} />
      {/* Sticky Search Bar */}
      <div className="sticky top-16 z-40 w-full bg-white/95 backdrop-blur-md border-b border-gray-200/50 shadow-sm">
        <div className="px-4 py-3 md:px-12">
          <HomeSearchToolbar
            address={address}
            onAddressChange={handleAddressChange}
            isMapApiLoaded={isLoaded}
            googleMapsApiKey={googleMapsApiKey}
            libraries={libraries}
            dishName={dishName}
            name={title}
            offering={offering}
            onSearchClick={handleSearch}
            onDishSelect={dishSelectFilter}
            onSearchName={dateFilter}
            onDineInSelect={offeringFilter}
            cuisines={allCuisines}
            selectedCuisine={cuisinequery}
            onCuisineSelect={cuisineFilter}
            onClearFilters={handleClearFilters}
            showSplitView={showSplitView}
            isLoading={isLoading}
          />
        </div>
      </div>
      {/* Main Content Area */}
      <div className="w-full min-h-screen flex flex-col pt-32">
        {/* Cuisine Tabs Container - Only show when not in split view */}
        {!showSplitView && (
          <div
            className={clsx(
              "transition-all duration-300 ease-in-out px-4 md:px-12",
              {
                // Styles when Drawer is OPEN - move to below search bar
                "fixed top-[128px] left-0 right-0 bg-white/95 backdrop-blur-md py-2 border-b shadow-sm z-30":
                  isDrawerOpen,
                // Styles when Drawer is CLOSED (normal flow)
                "py-4": !isDrawerOpen,
              }
            )}
          >
            <ScrollableTabs
              tabs={allCuisines}
              onCuisineSelect={cuisineFilter}
              currentCuisine={cuisinequery}
            />
          </div>
        )}
        {/* Content below filters */}
        <div className="flex-grow flex flex-col">
          {/* Split View Layout - Airbnb style - Only shown when search is active */}
          {showSplitView ? (
            <div className="h-full flex-grow overflow-hidden">
              {/* Height adjusted for header with search bar */}
              {/* Adjusted height to eliminate white space */}
              <SplitViewLayout
                isMapView={false} // Always show split view on desktop
                onViewChange={() => {}} // No-op on desktop
                listCount={mapHosts.length}
                listContent={
                  <div className="w-full flex flex-col justify-start items-start gap-6 md:gap-8 px-4 md:px-12 pb-8 pt-6">
                    <div className="w-full">
                      {/* Search Error Display */}
                      {searchError && (
                        <div
                          className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md"
                          role="alert"
                          aria-live="polite"
                        >
                          <div className="flex items-center">
                            <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
                            <span className="text-sm text-red-700">
                              {searchError}
                            </span>
                          </div>
                        </div>
                      )}

                      {/* Search Status and Results */}
                      {executedSearchParams.address ||
                      executedSearchParams.dishName ||
                      executedSearchParams.offering ||
                      executedSearchParams.cuisinequery ? (
                        <>
                          <div className="flex items-center justify-between">
                            <div>
                              <h2 className="text-xl font-semibold">
                                Search Results
                              </h2>
                              <p className="text-sm text-gray-500 mt-1">
                                {executedSearchParams.address
                                  ? `Near: ${executedSearchParams.address}`
                                  : ""}
                                {executedSearchParams.dishName
                                  ? (executedSearchParams.address
                                      ? " • "
                                      : "") +
                                    `Dish: ${executedSearchParams.dishName}`
                                  : ""}
                                {executedSearchParams.offering
                                  ? (executedSearchParams.address ||
                                    executedSearchParams.dishName
                                      ? " • "
                                      : "") + `${executedSearchParams.offering}`
                                  : ""}
                                {executedSearchParams.cuisinequery
                                  ? (executedSearchParams.address ||
                                    executedSearchParams.dishName ||
                                    executedSearchParams.offering
                                      ? " • "
                                      : "") +
                                    `${executedSearchParams.cuisinequery} cuisine`
                                  : ""}
                              </p>
                            </div>
                            {/* Search Status Indicator */}
                            {isLoading && (
                              <div className="flex items-center text-sm text-gray-500">
                                <div className="h-4 w-4 border border-gray-400 border-t-transparent rounded-full animate-spin mr-2" />
                                Searching...
                              </div>
                            )}
                          </div>
                          <div className="flex items-center justify-between mt-2">
                            <p className="text-sm font-medium">
                              {isLoading
                                ? "Searching..."
                                : `${results.length} dishes found`}
                            </p>
                            {hasSearched &&
                              !isLoading &&
                              results.length === 0 && (
                                <p className="text-sm text-gray-500 italic">
                                  No results found. Try adjusting your search.
                                </p>
                              )}
                          </div>
                        </>
                      ) : (
                        <div className="flex items-center justify-between">
                          <h2 className="text-xl font-semibold">All Dishes</h2>
                          {isLoading && (
                            <div className="flex items-center text-sm text-gray-500">
                              <div className="h-4 w-4 border border-gray-400 border-t-transparent rounded-full animate-spin mr-2" />
                              Loading...
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                    <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-6 md:gap-8">
                      {isLoading ? (
                        // Loading skeleton cards
                        Array(6)
                          .fill(0)
                          .map((_, index) => (
                            <DishCardSkeleton
                              key={index}
                              variant="default"
                              showSpinner={index === 0} // Show spinner only on first card
                            />
                          ))
                      ) : results.length > 0 ? (
                        // Use the results grouped by host for display
                        results.flatMap(
                          (host) =>
                            // Ensure dishes exist before mapping
                            host.dishes?.map((dish) => (
                              <div
                                key={dish._id}
                                className="w-full h-full rounded-lg overflow-hidden cursor-pointer"
                                onClick={() => handleDishClick(dish, host)}
                                onMouseEnter={() => setHoveredDishId(dish._id)}
                                onMouseLeave={() => setHoveredDishId(null)}
                              >
                                <HomeDishCard {...adaptDishForCard(dish)} />
                              </div>
                            )) ?? [] // Provide empty array if host.dishes is null/undefined
                        )
                      ) : (
                        <p className="text-gray-500 mt-10 text-center w-full">
                          No dishes found matching your criteria. Try adjusting
                          your search or filters.
                        </p>
                      )}

                      {/* Removed Create Dish Request Button from split layout */}
                    </div>
                  </div>
                }
                mapContent={
                  <GoogleMapComponent
                    isLoaded={isLoaded}
                    loadError={loadError}
                    googleMapsApiKey={googleMapsApiKey}
                    mapCenter={mapCenter}
                    mapZoom={mapZoom}
                    mapHosts={mapHosts}
                    selectedDish={selectedDish}
                    handleDishMarkerClick={handleDishMarkerClick}
                    handleInfoWindowClose={handleInfoWindowClose}
                    hoveredDishId={hoveredDishId}
                    onBoundsChange={handleMapBoundsChange}
                    dishes={dishes}
                  />
                }
              />
            </div>
          ) : (
            <>
              {/* Search Results Header */}
              <div
                className={clsx(
                  "w-full px-4 md:px-12 mb-4",
                  !isDrawerOpen ? "mt-6" : "mt-[180px]" // Adjust margin when drawer is open
                )}
              >
                {executedSearchParams.address ||
                executedSearchParams.dishName ||
                executedSearchParams.offering ||
                executedSearchParams.cuisinequery ? (
                  <>
                    <h2 className="text-xl font-semibold">Search Results</h2>
                    <p className="text-sm text-gray-500 mt-1">
                      {executedSearchParams.address
                        ? `Near: ${executedSearchParams.address}`
                        : ""}
                      {executedSearchParams.dishName
                        ? (executedSearchParams.address ? " • " : "") +
                          `Dish: ${executedSearchParams.dishName}`
                        : ""}
                      {executedSearchParams.offering
                        ? (executedSearchParams.address ||
                          executedSearchParams.dishName
                            ? " • "
                            : "") + `${executedSearchParams.offering}`
                        : ""}
                      {executedSearchParams.cuisinequery
                        ? (executedSearchParams.address ||
                          executedSearchParams.dishName ||
                          executedSearchParams.offering
                            ? " • "
                            : "") +
                          `${executedSearchParams.cuisinequery} cuisine`
                        : ""}
                    </p>
                    <p className="text-sm font-medium mt-2">
                      {results.length} dishes found
                    </p>
                  </>
                ) : (
                  <h2 className="text-xl font-semibold">All Dishes</h2>
                )}
              </div>

              {/* Dish Results Grid */}
              <div
                className={clsx(
                  "w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6 md:gap-8 px-4 md:px-12 pb-12",
                  // Add top padding only when drawer is closed and elements are sticky
                  !isDrawerOpen ? "pt-0" : "pt-[180px]" // Fixed padding when drawer is open to account for header + search + filters
                )}
              >
                {isLoading ? (
                  // Loading skeleton cards
                  Array(10)
                    .fill(0)
                    .map((_, index) => (
                      <DishCardSkeleton
                        key={index}
                        variant="default"
                        showSpinner={index === 0} // Show spinner only on first card
                      />
                    ))
                ) : results.length > 0 ? (
                  // Use the results grouped by host for display
                  results.flatMap(
                    (host) =>
                      // Ensure dishes exist before mapping
                      host.dishes?.map((dish) => (
                        <div
                          key={dish._id}
                          className="w-full h-full rounded-lg overflow-hidden cursor-pointer"
                          onClick={() => handleDishClick(dish, host)}
                          onMouseEnter={() => setHoveredDishId(dish._id)}
                          onMouseLeave={() => setHoveredDishId(null)}
                        >
                          <HomeDishCard {...adaptDishForCard(dish)} />
                        </div>
                      )) ?? [] // Provide empty array if host.dishes is null/undefined
                  )
                ) : (
                  <p className="text-gray-500 mt-10 text-center w-full">
                    No dishes found matching your criteria. Try adjusting your
                    search or filters.
                  </p>
                )}
              </div>

              {/* Create Dish Request Button */}
              <div
                className={clsx(
                  "w-full mx-auto flex justify-center items-center pb-8",
                  { hidden: isDrawerOpen } // Hide when drawer is open
                )}
              >
                <Button onClick={() => router.push("/requests/request-form")}>
                  Create Dish Request
                </Button>
              </div>
            </>
          )}
          {/* Mobile View - Toggle Button */}
          <div
            className={clsx(
              "fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40"
            )}
          >
            <Button
              onClick={() => setIsSplitView(!isSplitView)}
              className="flex items-center gap-2 py-3 px-5 rounded-full shadow-lg border border-gray-200 bg-white text-black hover:scale-105 transition-transform"
              variant="outline"
              disabled={!googleMapsApiKey || !isLoaded} // Disable if no key or maps not loaded
            >
              <MapPin size={16} className="text-black" />
              <span className="font-medium">
                {isSplitView ? "Show list" : "Show map"}
              </span>
            </Button>
          </div>
        </div>{" "}
        {/* End content below filters */}
      </div>{" "}
      {/* End main content container */}
      {/* --- Map Drawer (only shown when not in split view) --- */}
      {!showSplitView && (
        <CustomMapDrawer
          isOpen={isDrawerOpen}
          onClose={() => setIsDrawerOpen(false)}
          headerHeight={128} // Updated to account for header + search bar
        >
          <GoogleMapComponent
            isLoaded={isLoaded}
            loadError={loadError}
            googleMapsApiKey={googleMapsApiKey}
            mapCenter={mapCenter}
            mapZoom={mapZoom}
            mapHosts={mapHosts}
            selectedDish={selectedDish}
            handleDishMarkerClick={handleDishMarkerClick}
            handleInfoWindowClose={handleInfoWindowClose}
            hoveredDishId={hoveredDishId}
            onBoundsChange={handleMapBoundsChange}
            dishes={dishes}
          />
        </CustomMapDrawer>
      )}
      {!isSplitView && <Footer />}
      {/* --- End Map Drawer --- */}
      {/* Modals */}
      {showPhoneModal && (
        <PhoneNumberModal
          onSubmit={handlePhoneSubmit}
          onClose={() => setShowPhoneModal(false)}
        />
      )}
      {showAddressModal && (
        <AddressUserModal
          onSubmit={handleAddressSubmit}
          onClose={() => setShowAddressModal(false)}
          showSkip={true} // Keep skip option if desired
          // Pass googleMapsApiKey and libraries if AddressUserModal needs them for its own autocomplete
          googleMapsApiKey={googleMapsApiKey}
          libraries={libraries}
          isMapApiLoaded={isLoaded}
        />
      )}
    </React.Fragment>
  );
}
// --- End Component Logic ---

// Wrap the main component in Suspense
export default function HomePage() {
  return (
    <Suspense
      fallback={
        <div className="flex justify-center items-center min-h-screen">
          {/* Add a simple loading spinner or skeleton */}
          <div>Loading Delicious Dishes...</div>
        </div>
      }
    >
      <Home />
    </Suspense>
  );
}
